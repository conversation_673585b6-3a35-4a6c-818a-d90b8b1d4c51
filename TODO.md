TEST, seems still broken
tool output display is entirely broken, it needs to use the external printer
also, show (486 lines truncated) in red foreground text color

│
the rounded border in Agent interrupted or other messages like "copied last" needs the orange/blue color

show menu completion when /

ensure that there is newline at bottom of rendered ai message markdown, to better differentiate ai response to prompt input

fix 7. markdown demo display TwoThree in one line? they should be broken up

ensure flattened newlines before and after headings

using find and wc, refactor all files above 300 lines

this is wrong in tool_executor.rs, it should not be system message

add working in {git root dir} note on startup

let err_display_msg = AppMessage {
sender: MessageSender::System,
parts: vec![MessageContent::Text(err_text)],
};

needs runtime model change capabilities
add gemini 2.0 flash provider ((has tools))
add cohere provider (has tools)
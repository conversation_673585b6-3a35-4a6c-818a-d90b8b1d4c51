use rust_llm_tui::{app::{App, AppMessage, MessageContent, MessageSender}, display};
use reedline::ExternalPrinter;

fn main() {
    println!("=== External Printer Formatting Test ===\n");

    // Create an app instance
    let app = App::new(reqwest::Client::new());

    // Create test messages
    let user_message = AppMessage {
        sender: "User".to_string(),
        parts: vec![MessageContent::Text("Hello, this is a test user message!".to_string())],
    };
    
    let ai_markdown = r#"# AI Response Test

This is a test of the **external printer** formatting.

## Features Being Tested

1. Full-width user message borders
2. Proper AI message formatting with margins
3. Markdown elements like:
   - Lists
   - **Bold text**
   - `inline code`

### Code Example

```rust
fn test() {
    println!("External printer test");
}
```

That's the test!"#;
    
    let ai_message = app.create_ai_app_message_from_raw(ai_markdown);
    
    // Create external printer
    let external_printer = ExternalPrinter::default();
    
    println!("--- Regular Display ---");
    if let Err(e) = display::print_formatted_message(&user_message) {
        eprintln!("Error displaying user message: {}", e);
    }
    
    if let Err(e) = display::print_formatted_message(&ai_message) {
        eprintln!("Error displaying AI message: {}", e);
    }
    
    println!("\n--- External Printer Display ---");
    if let Err(e) = display::print_formatted_message_external(&user_message, &external_printer) {
        eprintln!("Error displaying user message via external printer: {}", e);
    }
    
    // Get the formatted message from external printer
    if let Some(formatted_user) = external_printer.get_line() {
        println!("{}", formatted_user);
    }
    
    if let Err(e) = display::print_formatted_message_external(&ai_message, &external_printer) {
        eprintln!("Error displaying AI message via external printer: {}", e);
    }
    
    // Get the formatted message from external printer
    if let Some(formatted_ai) = external_printer.get_line() {
        println!("{}", formatted_ai);
    }
    
    println!("\n=== Test Complete ===");
}

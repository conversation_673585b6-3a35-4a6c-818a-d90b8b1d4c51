use rust_llm_tui::{app::App, display};

fn main() {
    let demo_markdown = r#"# Improved Markdown Display

Welcome to the enhanced markdown display system!

## New Features

### 1. Enhanced Headings
- H1 headings now have beautiful borders
- Proper spacing before and after headings
- Centered text for better visual appeal

### 2. Improved Lists

#### Unordered Lists
- First item
- Second item
  - Nested item A
  - Nested item B
- Third item with `nested code`

#### Ordered Lists
1. First numbered item
2. Second numbered item
   1. Nested numbered item
   2. Another nested item
3. Third numbered item with `nested code`

### 3. Clickable Links
Check out these links:
- [Rust Language](https://rust-lang.org "The Rust Programming Language")
- [GitHub](https://github.com)
- [Documentation](https://doc.rust-lang.org/)
- Item with [link](https://example.com) inside

### 4. Beautiful Tables

| Feature | Status | Priority |
|---------|--------|----------|
| H1 Borders | ✅ Complete | High |
| Ordered Lists | ✅ Complete | High |
| Links | ✅ Complete | Medium |
| Tables | ✅ Complete | Medium |

| Left Aligned | Center Aligned | Right Aligned |
|:-------------|:--------------:|--------------:|
| Text | Centered | Numbers |
| More text | More centered | 123 |

### 5. Text Formatting
**bold text**, *italic text*, ~~strikethrough~~ and `inline code`

### 6. Code Blocks
```rust
fn main() {
    println!("Hello, enhanced markdown!");
    let features = vec!["borders", "tables", "links"];
    for feature in features {
        println!("✅ {}", feature);
    }
}
```

### 7. Newlines around lists

One

- First item
- Second item

Two

Three

1. First item
2. Second item

Four

### 8. Horizontal Ruler

---"#;
    let app = App::new(reqwest::Client::new());
    let app_message = app.create_ai_app_message_from_raw(demo_markdown);
    if let Err(e) = display::print_formatted_message(&app_message) {
        eprintln!("Error displaying message: {}", e);
    }
}

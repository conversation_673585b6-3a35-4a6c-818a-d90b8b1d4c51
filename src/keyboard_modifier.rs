use crossterm::event::PopKeyboardEnhancementFlags;
use crossterm::{
    event::{KeyboardEnhancementFlags, PushKeyboardEnhancementFlags},
    execute,
};
use std::io::{self};

pub fn enable_kitty_keyboard_for_meta_alt_modifiers() {
    execute!(
        io::stdout(),
        PushKeyboardEnhancementFlags(KeyboardEnhancementFlags::DISAMBIGUATE_ESCAPE_CODES)
    )
    .expect("Failed to enable kitty keyboard protocol for meta+alt modifiers");
}

pub fn disable_kitty_keyboard_for_meta_alt_modifiers() {
    execute!(io::stdout(), PopKeyboardEnhancementFlags)
        .expect("Failed to disable kitty keyboard protocol for meta+alt modifiers");
}

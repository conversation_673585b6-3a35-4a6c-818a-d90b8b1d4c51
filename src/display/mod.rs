// Re-export all display functionality
pub mod ai_message;
pub mod common;
pub mod system_message;
pub mod user_message;
mod colors;

use crate::app::{AppMessage, MessageSender};
pub use common::truncate_tool_output;
use std::io;

use reedline::ExternalPrinter;
use crate::display::colors::{BLUE_COLOR, ORANGE_COLOR};
use crossterm::style::Stylize;

pub fn print_formatted_message(app_msg: &AppMessage) -> io::Result<()> {
    match app_msg.sender {
        MessageSender::User => user_message::print_user_message(app_msg),
        MessageSender::AI => ai_message::print_ai_message(app_msg),
        MessageSender::System | MessageSender::ToolExecution => system_message::print_system_message(app_msg),
    }
}

pub fn print_formatted_message_external(
    app_msg: &AppMessage,
    external_printer: &ExternalPrinter<String>,
) -> Result<(), Box<dyn std::error::Error>> {
    // Format the message using the same logic as the regular display functions
    let formatted_message = match app_msg.sender {
        MessageSender::User => format_user_message_as_string(app_msg),
        MessageSender::AI => format_ai_message_as_string(app_msg),
        MessageSender::System | MessageSender::ToolExecution => format_system_message_as_string(app_msg),
    };

    // Send the formatted message through the external printer
    external_printer.print(formatted_message)?;

    Ok(())
}

fn format_user_message_as_string(app_msg: &AppMessage) -> String {
    use crossterm::style::Stylize;

    // Get the text content
    let text_content = if let Some(crate::app::MessageContent::Text(text)) = app_msg.parts.first() {
        text.lines().next().unwrap_or("").trim()
    } else {
        ""
    };

    // Format the text with color and simple prompt
    let colored_text = format!("{}", text_content.cyan());

    // Simple format: "> text"
    format!("> {}", colored_text)
}

fn format_ai_message_as_string(app_msg: &AppMessage) -> String {
    // This is a simplified version that captures the essential AI message formatting
    // For the external printer, we'll use a more basic but still nicely formatted approach
    let mut result = String::new();

    use crossterm::terminal;
    let (terminal_width, _) = terminal::size().unwrap_or((80, 24));

    result.push('\n'); // Initial newline for AI message block

    // Draw top border for AI response
    let border_width = terminal_width as usize;
    let top_border = format!("╭{}╮", "─".repeat(border_width.saturating_sub(2)));
    result.push_str(&format!("{}\n", top_border));

    for part in &app_msg.parts {
        match part {
            crate::app::MessageContent::Text(text_content) => {
                // Add border margins for all AI messages
                for line in text_content.lines() {
                    result.push_str(&format!("│ {} │\n", line));
                }
            }
            crate::app::MessageContent::FormattedText(elements) => {
                // Process formatted elements with basic formatting
                for element in elements {
                    match element {
                        crate::app::FormattedTextElement::ListStart => {
                            // Add a newline before list
                            result.push('\n');
                        }
                        crate::app::FormattedTextElement::ListEnd => {
                            // Add a newline after list
                            result.push('\n');
                        }
                        crate::app::FormattedTextElement::Text(text) => {
                            for line in text.lines() {
                                result.push_str(&format!("│ {} │\n", line));
                            }
                        }
                        crate::app::FormattedTextElement::Heading { level, text } => {
                            result.push('\n');
                            match level {
                                1 => {
                                    // Special border formatting for H1
                                    let border = "━".repeat(text.len() + 4);
                                    result.push_str(&format!("│ ┏{}┓ │\n", border));
                                    result.push_str(&format!("│ ┃  {}  ┃ │\n", text));
                                    result.push_str(&format!("│ ┗{}┛ │\n", border));
                                }
                                _ => {
                                    result.push_str(&format!("│ {} │\n", text));
                                }
                            }
                            result.push('\n');
                        }
                        crate::app::FormattedTextElement::ListItem { text, indent_level, is_ordered, number, .. } => {
                            let indent = "  ".repeat(*indent_level as usize + 1); // +1 for base margin
                            let bullet = if *is_ordered {
                                if let Some(num) = number {
                                    format!("{}.", num)
                                } else {
                                    "1.".to_string()
                                }
                            } else {
                                "•".to_string()
                            };
                            // Use colored bullet for list items
                            result.push_str(&format!("{}{} {}\n", indent, bullet, text));
                        }
                        crate::app::FormattedTextElement::InlineCode(text) => {
                            result.push_str(&format!("`{}`", text));
                        }
                        crate::app::FormattedTextElement::BoldOrItalic(text) => {
                            result.push_str(text); // Skip bold formatting for external printer
                        }
                        crate::app::FormattedTextElement::Link { text, .. } => {
                            result.push_str(text); // Just show the text for external printer
                        }
                        crate::app::FormattedTextElement::HorizontalRuler => {
                            result.push_str("  ───\n");
                        }
                        crate::app::FormattedTextElement::LineBreak => {
                            result.push('\n');
                        }
                        crate::app::FormattedTextElement::Strikethrough(text) => {
                            result.push_str(text); // Skip strikethrough formatting for external printer
                        }
                        crate::app::FormattedTextElement::Table { headers, rows, .. } => {
                            // Simple table representation with proper margins
                            result.push_str(&format!("  {}\n", headers.join(" | ")));
                            for row in rows {
                                result.push_str(&format!("  {}\n", row.join(" | ")));
                            }
                        }
                    }
                }
            }
            crate::app::MessageContent::CodeBlock { language, content } => {
                result.push_str(&format!("  ```{}\n", language.as_deref().unwrap_or("")));
                for line in content.lines() {
                    result.push_str(&format!("  {}\n", line));
                }
                result.push_str("  ```\n");
            }
            crate::app::MessageContent::ToolCall(tool_call) => {
                result.push_str(&format!("  └── Calling {}: {}\n",
                    tool_call.tool_name,
                    truncate_tool_output(&format!("{:?}", tool_call.arguments))
                ));
            }
        }
    }

    // Draw bottom border for AI response
    let bottom_border = format!("╰{}╯", "─".repeat(border_width.saturating_sub(2)));
    result.push_str(&format!("{}\n", bottom_border));

    result
}

/// Display a command output with blue rounded border
pub fn print_command_output_external(
    message: &str,
    external_printer: &ExternalPrinter<String>,
) -> Result<(), Box<dyn std::error::Error>> {
    let (terminal_width, _) = crossterm::terminal::size().unwrap_or((80, 24));
    let width = terminal_width as usize;

    // Create blue rounded border
    let top_border = format!("╭{}╮", "─".repeat(width.saturating_sub(2)));
    let bottom_border = format!("╰{}╯", "─".repeat(width.saturating_sub(2)));

    let mut output = String::new();
    output.push_str(&format!("{}\n", top_border.with(BLUE_COLOR)));

    // Add message content with padding
    for line in message.lines() {
        let content_width = width.saturating_sub(4); // Account for borders and padding
        if line.len() > content_width {
            // Wrap long lines
            for chunk in line.chars().collect::<Vec<_>>().chunks(content_width) {
                let chunk_str: String = chunk.iter().collect();
                output.push_str(&format!("│ {} │\n",
                    format!("{:<width$}", chunk_str, width = content_width).with(BLUE_COLOR)));
            }
        } else {
            output.push_str(&format!("│ {} │\n",
                format!("{:<width$}", line, width = content_width).with(BLUE_COLOR)));
        }
    }

    output.push_str(&format!("{}\n", bottom_border.with(BLUE_COLOR)));

    external_printer.print(output)?;
    Ok(())
}

/// Display agent interrupted message with orange rounded border
pub fn print_agent_interrupted_external(
    external_printer: &ExternalPrinter<String>,
) -> Result<(), Box<dyn std::error::Error>> {
    let (terminal_width, _) = crossterm::terminal::size().unwrap_or((80, 24));
    let width = terminal_width as usize;

    let message = "Agent interrupted";
    let content_width = width.saturating_sub(4); // Account for borders and padding

    // Create orange rounded border
    let top_border = format!("╭{}╮", "─".repeat(width.saturating_sub(2)));
    let bottom_border = format!("╰{}╯", "─".repeat(width.saturating_sub(2)));

    let mut output = String::new();
    output.push('\n'); // Newline above
    output.push_str(&format!("{}\n", top_border.with(ORANGE_COLOR)));
    output.push_str(&format!("│ {} │\n",
        format!("{:<width$}", message, width = content_width).with(ORANGE_COLOR)));
    output.push_str(&format!("{}\n", bottom_border.with(ORANGE_COLOR)));
    output.push('\n'); // Newline below

    external_printer.print(output)?;
    Ok(())
}

fn format_system_message_as_string(app_msg: &AppMessage) -> String {
    let mut result = String::new();

    let sender_prefix = format!("{}: ", app_msg.sender.as_str());

    if app_msg.parts.is_empty() {
        result.push_str(&sender_prefix);
        result.push('\n');
    } else {
        let mut first_line = true;
        for part in &app_msg.parts {
            match part {
                crate::app::MessageContent::Text(text_content) => {
                    for line in text_content.lines() {
                        if first_line {
                            result.push_str(&format!("{}{}\n", sender_prefix, line));
                            first_line = false;
                        } else {
                            let padding = " ".repeat(sender_prefix.len());
                            result.push_str(&format!("{}{}\n", padding, line));
                        }
                    }
                }
                _ => {
                    if first_line {
                        result.push_str(&format!("{}[Non-text content]\n", sender_prefix));
                        first_line = false;
                    }
                }
            }
        }
    }

    result.push('\n'); // Ensure a blank line after the message block
    result
}



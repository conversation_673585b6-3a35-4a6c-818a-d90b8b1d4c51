use crate::app::{AppMessage, MessageContent, MessageSender};
use crossterm::style::{Print, Stylize};
use std::io::{self, stdout, Write};
use textwrap::Options;

pub fn print_system_message(app_msg: &AppMessage) -> io::Result<()> {
    let mut out = stdout(); // Use stdout for more control with crossterm
    let (terminal_width, _) = crossterm::terminal::size().unwrap_or((80, 24)); // Default width 80

    // This part also might benefit from wrapping if text can be long.
    let sender_prefix_str = match app_msg.sender {
        MessageSender::System | MessageSender::ToolExecution => format!("{}: ", app_msg.sender.as_str()), // Apply styling later
        _ => format!("{}: ", app_msg.sender.as_str()),
    };

    // Calculate the prefix length before styling (for indentation)
    let prefix_len = sender_prefix_str.len();

    let styled_sender_prefix = match app_msg.sender.as_str() {
        "System" | "Tool Execution" => sender_prefix_str.clone().magenta().bold(),
        _ => sender_prefix_str.clone().bold(),
    };

    let mut first_line_of_message = true;
    if app_msg.parts.is_empty() {
        crossterm::execute!(
            out,
            Print(format!("{}\n", styled_sender_prefix.to_string().trim_end()))
        )?;
    } else {
        for part in &app_msg.parts {
            match part {
                MessageContent::Text(text_content) => {
                    print_text_content(
                        &mut out,
                        text_content,
                        &styled_sender_prefix,
                        &prefix_len,
                        terminal_width,
                        &mut first_line_of_message,
                    )?;
                }
                _ => {
                    print_unsupported_content(
                        &mut out,
                        &styled_sender_prefix,
                        &mut first_line_of_message,
                    )?;
                }
            }
        }
    }
    if !first_line_of_message || app_msg.parts.is_empty() {
        crossterm::execute!(out, Print("\n"))?; // Ensure a blank line after the message block
    }

    out.flush() // Ensure all buffered output is written
}

fn print_text_content(
    out: &mut std::io::Stdout,
    text_content: &str,
    styled_sender_prefix: &crossterm::style::StyledContent<String>,
    prefix_len: &usize,
    terminal_width: u16,
    first_line_of_message: &mut bool,
) -> io::Result<()> {
    // Could also wrap this if it can be long
    let available_width_for_system = terminal_width.saturating_sub(*prefix_len as u16);
    let options = Options::new(available_width_for_system as usize);
    // Create the indentation string once
    let indent = " ".repeat(*prefix_len);

    for line_str in text_content.lines() {
        if *first_line_of_message {
            // First line gets the prefix
            let wrapped_lines = textwrap::wrap(line_str, &options);
            if wrapped_lines.is_empty() {
                // Handle empty line
                crossterm::execute!(out, Print(styled_sender_prefix.clone()), Print("\n"))?;
            } else {
                // Print first wrapped line with prefix
                crossterm::execute!(
                    out,
                    Print(styled_sender_prefix.clone()),
                    Print(&wrapped_lines[0]),
                    Print("\n")
                )?;

                // Print remaining wrapped lines with proper indentation
                for wrapped_line in &wrapped_lines[1..] {
                    crossterm::execute!(
                        out,
                        Print(indent.clone()),
                        Print(wrapped_line),
                        Print("\n")
                    )?;
                }
            }
            *first_line_of_message = false;
        } else {
            // Subsequent lines get wrapped with proper indentation
            let wrapped_lines = textwrap::wrap(line_str, &options);
            for wrapped_line in wrapped_lines {
                crossterm::execute!(out, Print(indent.clone()), Print(wrapped_line), Print("\n"))?;
            }
        }
    }

    if text_content.is_empty() && *first_line_of_message {
        crossterm::execute!(
            out,
            Print(format!("{}\n", styled_sender_prefix.to_string().trim_end()))
        )?;
        *first_line_of_message = false;
    }

    Ok(())
}

fn print_unsupported_content(
    out: &mut std::io::Stdout,
    styled_sender_prefix: &crossterm::style::StyledContent<String>,
    first_line_of_message: &mut bool,
) -> io::Result<()> {
    let unsupported_msg = "[Unsupported content type for this sender]";
    if *first_line_of_message {
        crossterm::execute!(
            out,
            Print(styled_sender_prefix.clone()),
            Print(unsupported_msg),
            Print("\n")
        )?;
        *first_line_of_message = false;
    } else {
        crossterm::execute!(out, Print(unsupported_msg), Print("\n"))?;
    }
    Ok(())
}

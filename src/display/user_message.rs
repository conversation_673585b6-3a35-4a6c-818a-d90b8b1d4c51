use crate::app::{AppMessage, MessageContent};
use crossterm::style::{Print, Stylize};
use std::io::{self, stdout, Write};

pub fn print_user_message(app_msg: &AppMessage) -> io::Result<()> {
    let mut out = stdout(); // Use stdout for more control with crossterm

    // Get the text content
    let text_content = if let Some(MessageContent::Text(text)) = app_msg.parts.first() {
        text.lines().next().unwrap_or("").trim()
    } else {
        ""
    };

    // Format the text with color and simple prompt
    let colored_text = format!("{}", text_content.cyan());

    // Simple format: "> text"
    crossterm::execute!(out, Print(format!("> {}\n", colored_text)))?;

    out.flush() // Ensure all buffered output is written
}

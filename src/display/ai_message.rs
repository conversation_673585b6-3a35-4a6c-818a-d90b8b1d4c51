use crate::app::{AppMessage, FormattedTextElement, MessageContent, TableAlignment};
// For measuring visible width of text with ANSI codes
use crate::display::colors::{
    BOLD_AND_ITALIC_BACKGROUND_COLOR, CODE_BLOCK_BACKGROUND, HEADING_H4_COLOR, INLINE_CODE_COLOR,
    LIST_AND_RULE_CIRCLE_COLOR,
};
use crate::syntax_highlighting;
use console::measure_text_width;
use crossterm::style::{
    Attribute, Color, Print, ResetColor, SetAttribute, SetBackgroundColor, SetForegroundColor,
    Stylize,
};
use std::io::{self, stdout, Write};
use textwrap::{Options, WordSplitter};

fn print_formatted_text_content(
    out: &mut io::Stdout,
    elements: &[FormattedTextElement],
    outer_left_margin: &str,
    outer_right_margin: &str,
    content_wrap_width: u16,
) -> io::Result<()> {
    let mut current_line = String::new();
    let mut need_spacing_before_next = false;
    let mut prev_was_list_end = false;
    let mut prev_was_text = false;

    for element in elements {
        match element {
            FormattedTextElement::Heading { level, text } => {
                // Flush any current line content
                if !current_line.is_empty() {
                    print_wrapped_line(
                        out,
                        &current_line,
                        outer_left_margin,
                        outer_right_margin,
                        content_wrap_width,
                    )?;
                    current_line.clear();
                }

                // Add spacing before heading if needed
                if need_spacing_before_next {
                    print_empty_line(out, outer_left_margin, outer_right_margin, content_wrap_width)?;
                }

                // Add spacing before heading, but only if previous element wasn't a list end
                // (which already adds a newline)
                if !prev_was_list_end {
                    crossterm::execute!(out, Print("\n"))?;
                }

                match level {
                    1 => {
                        // Special border formatting for H1
                        print_h1_with_border(
                            out,
                            text,
                            outer_left_margin,
                            outer_right_margin,
                            content_wrap_width,
                        )?;
                    }
                    2 | 5 => {
                        let styled_heading = format!(
                            "{}{}{}",
                            SetAttribute(Attribute::Underlined),
                            text,
                            ResetColor
                        );
                        print_centered_line(
                            out,
                            &styled_heading,
                            outer_left_margin,
                            outer_right_margin,
                            content_wrap_width,
                        )?;
                    }
                    4 => {
                        let styled_heading = format!(
                            "{}{}{}",
                            SetForegroundColor(HEADING_H4_COLOR),
                            text,
                            ResetColor
                        );
                        print_centered_line(
                            out,
                            &styled_heading,
                            outer_left_margin,
                            outer_right_margin,
                            content_wrap_width,
                        )?;
                    }
                    _ => {
                        print_centered_line(
                            out,
                            text,
                            outer_left_margin,
                            outer_right_margin,
                            content_wrap_width,
                        )?;
                    }
                }

                // Add spacing after heading
                print_empty_line(out, outer_left_margin, outer_right_margin, content_wrap_width)?;
                need_spacing_before_next = false;
                prev_was_list_end = false;
                prev_was_text = false;
            }
            FormattedTextElement::Strikethrough(text) => {
                let styled_text = format!(
                    "{}{}{}",
                    SetAttribute(Attribute::CrossedOut),
                    text,
                    ResetColor
                );
                current_line.push_str(&styled_text);
                prev_was_list_end = false;
                prev_was_text = false;
            }
            FormattedTextElement::BoldOrItalic(text) => {
                let styled_text = format!(
                    "{}{}{}",
                    SetBackgroundColor(BOLD_AND_ITALIC_BACKGROUND_COLOR),
                    text,
                    ResetColor
                );
                current_line.push_str(&styled_text);
                prev_was_list_end = false;
                prev_was_text = false;
            }
            FormattedTextElement::InlineCode(text) => {
                let styled_text = format!(
                    "{}{}{}",
                    SetForegroundColor(INLINE_CODE_COLOR),
                    text,
                    ResetColor
                );
                current_line.push_str(&styled_text);
                prev_was_list_end = false;
                prev_was_text = false;
            }
            FormattedTextElement::ListStart => {
                // Add a newline before the list starts
                if !current_line.is_empty() {
                    print_wrapped_line(
                        out,
                        &current_line,
                        outer_left_margin,
                        outer_right_margin,
                        content_wrap_width,
                    )?;
                    current_line.clear();
                }
                
                // Add an extra newline before the list
                print_empty_line(out, outer_left_margin, outer_right_margin, content_wrap_width)?;
                need_spacing_before_next = false;
                prev_was_list_end = false;
                prev_was_text = false;
            },
            FormattedTextElement::ListEnd => {
                // Add a newline after the list ends
                if !current_line.is_empty() {
                    print_wrapped_line(
                        out,
                        &current_line,
                        outer_left_margin,
                        outer_right_margin,
                        content_wrap_width,
                    )?;
                    current_line.clear();
                }
                
                // Add an extra newline after the list
                print_empty_line(out, outer_left_margin, outer_right_margin, content_wrap_width)?;
                need_spacing_before_next = false;
                prev_was_list_end = true;
                prev_was_text = false;
            },
            FormattedTextElement::ListItem { indent_level, text, is_ordered, number } => {
                // Flush any current line content
                if !current_line.is_empty() {
                    print_wrapped_line(
                        out,
                        &current_line,
                        outer_left_margin,
                        outer_right_margin,
                        content_wrap_width,
                    )?;
                    current_line.clear();
                }

                let indent = "  ".repeat(*indent_level as usize);
                let bullet = if *is_ordered {
                    if let Some(num) = number {
                        format!("{}.", num)
                    } else {
                        "1.".to_string()
                    }
                } else {
                    "•".to_string()
                };

                let formatted_line = format!(
                    "{}{}{}{} {}",
                    SetForegroundColor(LIST_AND_RULE_CIRCLE_COLOR),
                    indent,
                    bullet,
                    ResetColor,
                    text
                );
                print_wrapped_line(
                    out,
                    &formatted_line,
                    outer_left_margin,
                    outer_right_margin,
                    content_wrap_width,
                )?;
                need_spacing_before_next = false;
                prev_was_list_end = false;
                prev_was_text = false;
            }
            FormattedTextElement::Link { text, url, title } => {
                let link_display = if let Some(title_text) = title {
                    format!("{}[{}]({}) \"{}\"",
                        SetForegroundColor(Color::Blue),
                        text,
                        url,
                        title_text
                    )
                } else {
                    format!("{}[{}]({})",
                        SetForegroundColor(Color::Blue),
                        text,
                        url
                    )
                };
                current_line.push_str(&link_display);
                current_line.push_str(&format!("{}", ResetColor));
                prev_was_list_end = false;
                prev_was_text = false;
            }
            FormattedTextElement::Table { headers, rows, alignments } => {
                // Flush any current line content
                if !current_line.is_empty() {
                    print_wrapped_line(
                        out,
                        &current_line,
                        outer_left_margin,
                        outer_right_margin,
                        content_wrap_width,
                    )?;
                    current_line.clear();
                }

                print_table(
                    out,
                    headers,
                    rows,
                    alignments,
                    outer_left_margin,
                    outer_right_margin,
                    content_wrap_width,
                )?;
                need_spacing_before_next = false;
                prev_was_list_end = false;
                prev_was_text = false;
            }
            FormattedTextElement::Text(text) => {
                // Add a space before this text if the previous element was also text
                if prev_was_text && !current_line.is_empty() {
                    current_line.push(' ');
                }
                current_line.push_str(text);
                prev_was_list_end = false;
                prev_was_text = true;
            }
            FormattedTextElement::HorizontalRuler => {
                let ruler = "─".repeat(content_wrap_width as usize);
                crossterm::execute!(
                    out,
                    Print(format!(
                        "{}{}{}{}{}\n",
                        SetForegroundColor(LIST_AND_RULE_CIRCLE_COLOR),
                        outer_left_margin,
                        ruler,
                        outer_right_margin,
                        ResetColor
                    ))
                )?;
                prev_was_list_end = false;
                prev_was_text = false;
            }
            FormattedTextElement::LineBreak => {
                if !current_line.is_empty() {
                    print_wrapped_line(
                        out,
                        &current_line,
                        outer_left_margin,
                        outer_right_margin,
                        content_wrap_width,
                    )?;
                    current_line.clear();
                } else {
                    print_empty_line(out, outer_left_margin, outer_right_margin, content_wrap_width)?;
                }
                need_spacing_before_next = false;
                prev_was_list_end = false;
                prev_was_text = false;
            }
        }
    }

    // Flush any remaining content
    if !current_line.is_empty() {
        print_wrapped_line(
            out,
            &current_line,
            outer_left_margin,
            outer_right_margin,
            content_wrap_width,
        )?;
    }

    // Add a newline after formatted text blocks for consistent spacing
    crossterm::execute!(out, Print("\n"))?;
    Ok(())
}

fn print_empty_line(
    out: &mut io::Stdout,
    outer_left_margin: &str,
    outer_right_margin: &str,
    content_wrap_width: u16,
) -> io::Result<()> {
    let padding = " ".repeat(content_wrap_width as usize);
    crossterm::execute!(
        out,
        Print(format!("{}{}{}\n", outer_left_margin, padding, outer_right_margin))
    )
}

fn print_centered_line(
    out: &mut io::Stdout,
    content: &str,
    outer_left_margin: &str,
    outer_right_margin: &str,
    content_wrap_width: u16,
) -> io::Result<()> {
    let content_width = measure_text_width(content);
    let available_width = content_wrap_width as usize;

    if content_width < available_width {
        let left_padding = (available_width - content_width) / 2;
        let right_padding = available_width - content_width - left_padding;
        let left_padding_str = " ".repeat(left_padding);
        let right_padding_str = " ".repeat(right_padding);
        crossterm::execute!(
            out,
            Print(format!(
                "{}{}{}{}{}\n",
                outer_left_margin, left_padding_str, content, right_padding_str, outer_right_margin
            ))
        )?;
    } else {
        // If content is too long, just print it normally
        print_wrapped_line(
            out,
            content,
            outer_left_margin,
            outer_right_margin,
            content_wrap_width,
        )?;
    }
    Ok(())
}

fn print_wrapped_line(
    out: &mut io::Stdout,
    content: &str,
    outer_left_margin: &str,
    outer_right_margin: &str,
    content_wrap_width: u16,
) -> io::Result<()> {
    if content.is_empty() {
        print_empty_line(out, outer_left_margin, outer_right_margin, content_wrap_width)?;
        return Ok(());
    }

    let options = Options::new(content_wrap_width as usize)
        .word_separator(textwrap::WordSeparator::AsciiSpace)
        .subsequent_indent("")
        .initial_indent("");

    let wrapped_lines = textwrap::wrap(content, &options);
    for wrapped_line in wrapped_lines {
        // Check if the line contains inline code and style it
        let styled_line = style_inline_code(&wrapped_line);

        // Calculate padding needed to fill the terminal width
        let visible_width = measure_text_width(&styled_line);
        let padding_needed = (content_wrap_width as usize).saturating_sub(visible_width);
        let padding = " ".repeat(padding_needed);

        crossterm::execute!(
            out,
            Print(format!(
                "{}{}{}{}\n",
                outer_left_margin, styled_line, padding, outer_right_margin
            ))
        )?;
    }
    Ok(())
}

// Helper function to style inline code and links in text
fn style_inline_code(text: &str) -> String {
    // First style any inline code
    let text_with_styled_code = style_code_segments(text);
    
    // Then style any links with special markers
    style_link_markers(&text_with_styled_code)
}

// Style inline code segments (text between backticks)
fn style_code_segments(text: &str) -> String {
    // If no backticks, return the original text
    if !text.contains('`') {
        return text.to_string();
    }
    
    let mut result = String::new();
    let mut remaining_text = text;
    
    // Process text with backticks
    while let Some(start_idx) = remaining_text.find('`') {
        // Add text before the first backtick
        result.push_str(&remaining_text[..start_idx]);
        
        // Look for the closing backtick
        if let Some(end_idx) = remaining_text[start_idx + 1..].find('`') {
            let code_content = &remaining_text[start_idx + 1..start_idx + 1 + end_idx];
            
            // Add the styled code (without backticks)
            result.push_str(&format!(
                "{}{}{}",
                SetForegroundColor(INLINE_CODE_COLOR),
                code_content,
                ResetColor
            ));
            
            // Update remaining text
            remaining_text = &remaining_text[start_idx + 1 + end_idx + 1..];
        } else {
            // No closing backtick found, just add the rest as is
            result.push_str(&remaining_text[start_idx..]);
            break;
        }
    }
    
    // Add any remaining text after the last backtick pair
    result.push_str(remaining_text);
    
    result
}

// Style link markers in text
fn style_link_markers(text: &str) -> String {
    // If no link markers, return the original text
    if !text.contains("__LINK_START__") {
        return text.to_string();
    }
    
    let mut result = String::new();
    let mut remaining_text = text;
    
    // Process text with link markers
    while let Some(start_idx) = remaining_text.find("__LINK_START__") {
        // Add text before the link marker
        result.push_str(&remaining_text[..start_idx]);
        
        // Look for the URL separator and end marker
        if let Some(url_idx) = remaining_text[start_idx..].find("__LINK_URL__") {
            if let Some(end_idx) = remaining_text[start_idx..].find("__LINK_END__") {
                let link_text = &remaining_text[
                    start_idx + "__LINK_START__".len()..
                    start_idx + url_idx
                ];
                
                let link_url = &remaining_text[
                    start_idx + url_idx + "__LINK_URL__".len()..
                    start_idx + end_idx
                ];
                
                // Add the styled link text with URL in markdown format for clickability
                result.push_str(&format!(
                    "{}[{}]({}){}",
                    SetForegroundColor(Color::Blue),
                    link_text,
                    link_url,
                    ResetColor
                ));
                
                // Update remaining text
                remaining_text = &remaining_text[start_idx + end_idx + "__LINK_END__".len()..];
            } else {
                // No end marker found, just add the start marker and continue
                result.push_str("__LINK_START__");
                remaining_text = &remaining_text[start_idx + "__LINK_START__".len()..];
            }
        } else {
            // No URL separator found, just add the start marker and continue
            result.push_str("__LINK_START__");
            remaining_text = &remaining_text[start_idx + "__LINK_START__".len()..];
        }
    }
    
    // Add any remaining text
    result.push_str(remaining_text);
    
    result
}

pub fn print_ai_message(app_msg: &AppMessage) -> io::Result<()> {
    let mut out = stdout(); // Use stdout for more control with crossterm
    let (terminal_width, _) = crossterm::terminal::size().unwrap_or((80, 24)); // Default width 80

    crossterm::execute!(out, Print("\n"))?; // Initial newline for AI message block

    // Draw top border for AI response
    let border_width = terminal_width as usize;
    let top_border = format!("╭{}╮", "─".repeat(border_width.saturating_sub(2)));
    crossterm::execute!(out, Print(format!("{}\n", top_border)))?;

    // Add 1-space left and right margin inside the border for all AI messages
    let outer_left_margin = "│ ";
    let outer_right_margin = " │";
    // Calculate the width available for content *between* the outer margins and borders
    let content_wrap_width = terminal_width
        .saturating_sub(outer_left_margin.len() as u16 + outer_right_margin.len() as u16);

    // Ensure content_wrap_width is at least 1 to avoid panic with textwrap
    let content_wrap_width = if content_wrap_width < 1 {
        1
    } else {
        content_wrap_width
    };

    for (idx, part) in app_msg.parts.iter().enumerate() {
        if idx > 0 {
            let prev_part = &app_msg.parts[idx - 1];
            let prev_ended_with_newline = match prev_part {
                MessageContent::Text(t) => t.is_empty() || t.ends_with('\n'),
                MessageContent::FormattedText(_) => true,
                MessageContent::CodeBlock { .. } | MessageContent::ToolCall(_) => true,
            };
            if !prev_ended_with_newline {
                crossterm::execute!(out, Print("\n"))?;
            }
        }

        match part {
            MessageContent::Text(text_content) => {
                print_text_content(
                    &mut out,
                    text_content,
                    outer_left_margin,
                    outer_right_margin,
                    content_wrap_width,
                )?;
            }
            MessageContent::FormattedText(elements) => {
                print_formatted_text_content(
                    &mut out,
                    elements,
                    outer_left_margin,
                    outer_right_margin,
                    content_wrap_width,
                )?;
            }
            MessageContent::CodeBlock { language, content } => {
                // Check if previous part was a FormattedText that might have ended with a heading
                let prev_might_be_heading = if idx > 0 {
                    matches!(&app_msg.parts[idx - 1], MessageContent::FormattedText(_))
                } else {
                    false
                };
                print_code_block(&mut out, language, content, terminal_width, prev_might_be_heading)?;
            }
            MessageContent::ToolCall(tool_call) => {
                print_tool_call(&mut out, tool_call, outer_left_margin, outer_right_margin)?;
            }
        }
    }
    
    // Check if the last part already ends with a newline
    let last_part_ends_with_newline = if let Some(last_part) = app_msg.parts.last() {
        match last_part {
            MessageContent::Text(t) => t.ends_with('\n'),
            // FormattedText, CodeBlock, and ToolCall already add newlines
            MessageContent::FormattedText(_) | 
            MessageContent::CodeBlock { .. } | 
            MessageContent::ToolCall(_) => true,
        }
    } else {
        false
    };
    
    // Add a final newline if the last part doesn't already end with one
    if !last_part_ends_with_newline {
        crossterm::execute!(out, Print("\n"))?;
    }

    // Draw bottom border for AI response
    let bottom_border = format!("╰{}╯", "─".repeat(border_width.saturating_sub(2)));
    crossterm::execute!(out, Print(format!("{}\n", bottom_border)))?;

    out.flush() // Ensure all buffered output is written
}

fn print_text_content(
    out: &mut io::Stdout,
    text_content: &str,
    outer_left_margin: &str,
    outer_right_margin: &str,
    content_wrap_width: u16,
) -> io::Result<()> {
    if text_content.is_empty() {
        return Ok(());
    }

    // For plain text content, just wrap and print without special formatting
    let options = Options::new(content_wrap_width as usize)
        .word_separator(textwrap::WordSeparator::AsciiSpace)
        .subsequent_indent("")
        .initial_indent("");

    for line in text_content.lines() {
        if line.trim().is_empty() && !line.is_empty() {
            // Preserve lines that were just spaces with proper padding
            let padding_needed = (content_wrap_width as usize).saturating_sub(line.len());
            let padding = " ".repeat(padding_needed);
            crossterm::execute!(
                out,
                Print(format!(
                    "{}{}{}{}\n",
                    outer_left_margin, line, padding, outer_right_margin
                ))
            )?;
        } else if line.is_empty() {
            // Preserve completely empty lines with proper padding
            print_empty_line(out, outer_left_margin, outer_right_margin, content_wrap_width)?;
        } else {
            let wrapped_lines = textwrap::wrap(line, &options);
            for wrapped_line in wrapped_lines {
                let visible_width = measure_text_width(&wrapped_line);
                let padding_needed = (content_wrap_width as usize).saturating_sub(visible_width);
                let padding = " ".repeat(padding_needed);
                crossterm::execute!(
                    out,
                    Print(format!(
                        "{}{}{}{}\n",
                        outer_left_margin, wrapped_line, padding, outer_right_margin
                    ))
                )?;
            }
        }
    }

    // Add a newline after text blocks for consistent spacing
    crossterm::execute!(out, Print("\n"))?;
    Ok(())
}

fn print_code_block(
    out: &mut io::Stdout,
    language: &Option<String>,
    content: &str,
    terminal_width: u16,
    prev_might_be_heading: bool,
) -> io::Result<()> {
    // Use the same margins as other content (inside the border)
    let outer_left_margin = "│ ";
    let outer_right_margin = " │";
    
    // Calculate the width available for content *between* the outer margins
    let content_width = terminal_width
        .saturating_sub(outer_left_margin.len() as u16 + outer_right_margin.len() as u16);
    
    // Only add an extra newline before the code block if the previous element wasn't a heading
    // (since headings already add their own newline)
    if !prev_might_be_heading {
        crossterm::execute!(out, Print("\n"))?;
    }
    
    // Top padding line for the code block (with proper margins)
    crossterm::execute!(
        out,
        Print(outer_left_margin),
        SetBackgroundColor(CODE_BLOCK_BACKGROUND),
        Print(" ".repeat(content_width as usize)),
        ResetColor,
        Print(outer_right_margin),
        Print("\n") // Newline after the padding line
    )?;

    let highlighted_code_full =
        syntax_highlighting::highlight_code_to_ansi_string(content, language.as_deref());

    // Margins INSIDE the code block's background
    let inner_left_margin = " "; // 1 space padding for inner code
    let inner_right_margin = " ";

    // Width available for code *between* the inner_left_margin and inner_right_margin
    let code_content_wrap_width = terminal_width
        .saturating_sub(inner_left_margin.len() as u16 + inner_right_margin.len() as u16);

    // Ensure code_content_wrap_width is at least 1
    let code_content_wrap_width = if code_content_wrap_width < 1 {
        1
    } else {
        code_content_wrap_width
    };

    // Configure textwrap for ANSI-highlighted code
    // We need special handling for ANSI escape codes
    let options = create_code_wrap_options(code_content_wrap_width);

    if content.is_empty() {
        // Handle empty code block: print a line with inner margins and background
        print_empty_code_line(
            out,
            &CODE_BLOCK_BACKGROUND,
            inner_left_margin,
            inner_right_margin,
            code_content_wrap_width,
        )?;
    } else {
        for original_line in highlighted_code_full.lines() {
            if original_line.is_empty() {
                // Handle empty lines within the code
                print_empty_code_line(
                    out,
                    &CODE_BLOCK_BACKGROUND,
                    inner_left_margin,
                    inner_right_margin,
                    code_content_wrap_width,
                )?;
                continue;
            }
            let wrapped_segments = textwrap::wrap(original_line, &options);
            for segment in wrapped_segments {
                print_code_line(
                    out,
                    &segment,
                    &CODE_BLOCK_BACKGROUND,
                    inner_left_margin,
                    inner_right_margin,
                    terminal_width,
                )?;
            }
        }
    }

    // Bottom padding line for the code block (with proper margins)
    crossterm::execute!(
        out,
        Print(outer_left_margin),
        SetBackgroundColor(CODE_BLOCK_BACKGROUND),
        Print(" ".repeat(content_width as usize)),
        ResetColor,
        Print(outer_right_margin),
        Print("\n") // Newline after the padding line
    )?;
    
    // We don't need an extra newline after code blocks anymore
    // The padding line already adds one newline
    Ok(())
}

fn create_code_wrap_options(code_content_wrap_width: u16) -> Options<'static> {
    Options::new(code_content_wrap_width as usize)
        // Use a custom word splitter that preserves ANSI escape codes
        .word_splitter(WordSplitter::Custom(|word| {
            // Simple implementation that splits at reasonable points
            // but avoids splitting inside ANSI escape sequences
            let mut splits = Vec::new();
            let mut in_ansi = false;
            let mut last_split = 0;

            for (i, c) in word.char_indices() {
                if c == '\x1B' {
                    // ESC character
                    in_ansi = true;
                } else if in_ansi && c == 'm' {
                    in_ansi = false;
                } else if !in_ansi && (c == '_' || c == '-') {
                    // Split after hyphens and underscores when not in ANSI sequence
                    splits.push(i + 1);
                    last_split = i + 1;
                }
            }

            // If the word is very long and we haven't found any good split points,
            // add some artificial ones every 10 characters, but not in ANSI sequences
            if splits.is_empty() && word.len() > 20 {
                in_ansi = false;
                for (i, c) in word.char_indices() {
                    if c == '\x1B' {
                        // ESC character
                        in_ansi = true;
                    } else if in_ansi && c == 'm' {
                        in_ansi = false;
                    } else if !in_ansi && i > last_split + 10 {
                        splits.push(i);
                        last_split = i;
                    }
                }
            }

            splits
        }))
        .break_words(true) // Break long tokens if they don't fit
}

fn print_empty_code_line(
    out: &mut io::Stdout,
    code_block_bg_color: &Color,
    inner_left_margin: &str,
    inner_right_margin: &str,
    code_content_wrap_width: u16,
) -> io::Result<()> {
    // Use the same margins as other content (inside the border)
    let outer_left_margin = "│ ";
    let outer_right_margin = " │";
    
    crossterm::execute!(
        out,
        Print(outer_left_margin),
        SetBackgroundColor(*code_block_bg_color),
        Print(inner_left_margin),
        Print(" ".repeat(code_content_wrap_width as usize)), // Fill the wrap width
        Print(inner_right_margin),
        ResetColor,
        Print(outer_right_margin),
        Print("\n")
    )
}

fn print_code_line(
    out: &mut io::Stdout,
    segment: &str,
    code_block_bg_color: &Color,
    inner_left_margin: &str,
    inner_right_margin: &str,
    terminal_width: u16,
) -> io::Result<()> {
    // Use the same margins as other content (inside the border)
    let outer_left_margin = "│ ";
    let outer_right_margin = " │";
    
    // Calculate the width available for content *between* the outer margins
    let content_width = terminal_width
        .saturating_sub(outer_left_margin.len() as u16 + outer_right_margin.len() as u16);
    
    // Print the line with proper margins
    crossterm::execute!(out, Print(outer_left_margin))?;
    crossterm::execute!(out, SetBackgroundColor(*code_block_bg_color))?;
    crossterm::execute!(out, Print(inner_left_margin))?;
    crossterm::execute!(out, Print(segment))?; // segment already has ANSI
    crossterm::execute!(out, Print(inner_right_margin))?;

    // Calculate padding to fill the rest of the content width
    let visible_width_of_segment_with_margins = inner_left_margin.len() +
        measure_text_width(segment) + // measure_text_width is ANSI-aware
        inner_right_margin.len();

    let line_padding_len =
        content_width.saturating_sub(visible_width_of_segment_with_margins as u16);
    if line_padding_len > 0 {
        crossterm::execute!(out, Print(" ".repeat(line_padding_len as usize)))?;
    }

    crossterm::execute!(out, ResetColor, Print(outer_right_margin), Print("\n"))
}

fn print_tool_call(
    out: &mut io::Stdout,
    tool_call: &crate::llm_client::ToolCallAction,
    outer_left_margin: &str,
    outer_right_margin: &str,
) -> io::Result<()> {
    // Format tool call in compact format: └── Calling tool_name: {"arg": "value"...
    let args_json = match serde_json::to_string(&tool_call.arguments) {
        Ok(json) => {
            // Truncate after 200 characters
            if json.len() > 200 {
                format!("{}...", &json[..200])
            } else {
                json
            }
        }
        Err(_) => "{}".to_string(),
    };

    let tool_call_line = format!("└── Calling {}: {}", tool_call.tool_name, args_json);

    crossterm::execute!(
        out,
        Print(outer_left_margin),
        Print(tool_call_line.cyan().bold()),
        Print(outer_right_margin),
        Print("\n")
    )?;

    // Always add a newline after tool calls for better visual separation
    crossterm::execute!(out, Print("\n"))
}

fn print_h1_with_border(
    out: &mut io::Stdout,
    text: &str,
    outer_left_margin: &str,
    outer_right_margin: &str,
    content_wrap_width: u16,
) -> io::Result<()> {
    let text_width = console::measure_text_width(text);
    
    // Use the full content width for the border
    let border_width = content_wrap_width as usize;

    // Top border: ┏━━━━━━━┓ spanning full width
    let top_border = format!("┏{}┓", "━".repeat(border_width.saturating_sub(2)));
    crossterm::execute!(
        out,
        Print(format!("{}{}{}\n", outer_left_margin, top_border, outer_right_margin))
    )?;

    // Content line with side borders and centered text
    let content_padding = (border_width.saturating_sub(text_width + 2)) / 2;
    let content_line = format!(
        "┃{}{}{}{}{}┃",
        " ".repeat(content_padding),
        SetForegroundColor(Color::Cyan),
        text,
        ResetColor,
        " ".repeat(border_width.saturating_sub(2 + content_padding + text_width))
    );
    crossterm::execute!(
        out,
        Print(format!("{}{}{}\n", outer_left_margin, content_line, outer_right_margin))
    )?;

    // Bottom border: ┗━━━━━━━┛ spanning full width
    let bottom_border = format!("┗{}┛", "━".repeat(border_width.saturating_sub(2)));
    crossterm::execute!(
        out,
        Print(format!("{}{}{}\n", outer_left_margin, bottom_border, outer_right_margin))
    )?;

    Ok(())
}

fn print_table(
    out: &mut io::Stdout,
    headers: &[String],
    rows: &[Vec<String>],
    alignments: &[TableAlignment],
    outer_left_margin: &str,
    outer_right_margin: &str,
    _content_wrap_width: u16,
) -> io::Result<()> {
    if headers.is_empty() {
        return Ok(());
    }

    // Calculate column widths
    let mut col_widths = Vec::new();
    for (i, header) in headers.iter().enumerate() {
        let mut max_width = console::measure_text_width(header);
        for row in rows {
            if let Some(cell) = row.get(i) {
                max_width = max_width.max(console::measure_text_width(cell));
            }
        }
        col_widths.push(max_width + 2); // +2 for padding
    }

    // Print header
    let header_line = format_table_row(headers, &col_widths, alignments);
    crossterm::execute!(
        out,
        Print(format!("{}{}{}\n", outer_left_margin, header_line, outer_right_margin))
    )?;

    // Print separator
    let separator = col_widths.iter().map(|&w| "─".repeat(w)).collect::<Vec<_>>().join("┼");
    let separator_line = format!("├{}┤", separator);
    crossterm::execute!(
        out,
        Print(format!("{}{}{}\n", outer_left_margin, separator_line, outer_right_margin))
    )?;

    // Print rows
    for row in rows {
        let row_line = format_table_row(row, &col_widths, alignments);
        crossterm::execute!(
            out,
            Print(format!("{}{}{}\n", outer_left_margin, row_line, outer_right_margin))
        )?;
    }

    Ok(())
}

fn format_table_row(
    cells: &[String],
    col_widths: &[usize],
    alignments: &[TableAlignment],
) -> String {
    let mut result = String::from("│");

    for (i, cell) in cells.iter().enumerate() {
        let width = col_widths.get(i).copied().unwrap_or(10);
        let alignment = alignments.get(i).unwrap_or(&TableAlignment::Left);

        let cell_content = match alignment {
            TableAlignment::Left | TableAlignment::None => {
                format!(" {:<width$} ", cell, width = width.saturating_sub(2))
            }
            TableAlignment::Right => {
                format!(" {:>width$} ", cell, width = width.saturating_sub(2))
            }
            TableAlignment::Center => {
                let padding = width.saturating_sub(cell.len() + 2);
                let left_pad = padding / 2;
                let right_pad = padding - left_pad;
                format!(" {}{}{} ", " ".repeat(left_pad), cell, " ".repeat(right_pad))
            }
        };

        result.push_str(&cell_content);
        result.push('│');
    }

    result
}

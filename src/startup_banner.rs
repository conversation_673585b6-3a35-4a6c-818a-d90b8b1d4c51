use crossterm::style::Stylize;

/// Prints a nicely formatted startup banner with agent info and session details
pub fn print_startup_banner(session_id: &str, proxy: Option<u16>) {
    let session_display = if session_id.starts_with("session_") {
        session_id.trim_start_matches("session_").to_string()
    } else {
        "unknown".into()
    };
    let proxy_info = if let Some(port) = proxy {
        format!("proxy:{}", port)
    } else {
        "direct".to_string()
    };
    println!(
        "\n   Dima AI Agent     grok-3-mini-high 🤖 Pollinations.AI    {}\n",
        format!("[{}, {}]", proxy_info, session_display).yellow()
    );
}
